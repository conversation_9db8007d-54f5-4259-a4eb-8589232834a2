import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  CircularProgress,
  Divider,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PlayArrow,
  CheckCircle,
  Error,
  Warning,
  School,
  People,
  Assignment,
  Add,
  TestTube
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد الخدمات
import { hybridStudents, hybridCourses } from '../../services/hybridDatabaseService';

const AddFunctionsTest = () => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState([]);
  const [testDialog, setTestDialog] = useState({
    open: false,
    type: '',
    title: ''
  });
  const [testData, setTestData] = useState({
    studentName: 'طالب اختبار',
    studentEmail: '<EMAIL>',
    studentPhone: '0501234567',
    courseTitle: 'كورس اختبار',
    courseDescription: 'هذا كورس للاختبار',
    courseLevel: 'مبتدئ'
  });

  // إضافة نتيجة اختبار
  const addTestResult = (message, type, details = null) => {
    const result = {
      id: Date.now(),
      message,
      type,
      details,
      timestamp: new Date().toLocaleTimeString('ar-SA')
    };
    
    setTestResults(prev => [...prev, result]);
    
    if (type === 'success') {
      toast.success(message);
    } else if (type === 'error') {
      toast.error(message);
    } else if (type === 'warning') {
      toast(message, { icon: '⚠️' });
    }
  };

  // اختبار إضافة طالب
  const testAddStudent = async () => {
    try {
      addTestResult('بدء اختبار إضافة طالب...', 'info');
      
      const studentData = {
        name: testData.studentName + ' ' + Date.now(),
        email: testData.studentEmail.replace('@', Date.now() + '@'),
        phone: testData.studentPhone,
        password: 'test123456'
      };

      const result = await hybridStudents.addStudent(studentData);
      
      if (result.success) {
        addTestResult(
          `✅ تم إضافة الطالب بنجاح - كود الطالب: ${result.studentCode}`,
          'success',
          {
            studentId: result.id,
            studentCode: result.studentCode,
            firebaseId: result.firebaseId,
            supabaseId: result.supabaseId
          }
        );
      } else {
        addTestResult('❌ فشل في إضافة الطالب', 'error');
      }
    } catch (error) {
      addTestResult(`❌ خطأ في إضافة الطالب: ${error.message}`, 'error');
    }
  };

  // اختبار إضافة كورس
  const testAddCourse = async () => {
    try {
      addTestResult('بدء اختبار إضافة كورس...', 'info');
      
      const courseData = {
        title: testData.courseTitle + ' ' + Date.now(),
        description: testData.courseDescription,
        level: testData.courseLevel,
        duration: '2 ساعات',
        price: 0,
        instructor: 'علاء عبد الحميد'
      };

      const result = await hybridCourses.addCourse(courseData);
      
      if (result.success) {
        addTestResult(
          `✅ تم إضافة الكورس بنجاح - ID: ${result.id}`,
          'success',
          {
            courseId: result.id,
            firebaseId: result.firebaseId,
            supabaseId: result.supabaseId
          }
        );
      } else {
        addTestResult('❌ فشل في إضافة الكورس', 'error');
      }
    } catch (error) {
      addTestResult(`❌ خطأ في إضافة الكورس: ${error.message}`, 'error');
    }
  };

  // اختبار شامل
  const runFullTest = async () => {
    setLoading(true);
    setTestResults([]);
    
    try {
      addTestResult('🚀 بدء الاختبار الشامل لوظائف الإضافة...', 'info');
      
      // اختبار 1: إضافة طالب
      await testAddStudent();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // اختبار 2: إضافة كورس
      await testAddCourse();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      addTestResult('🎉 انتهى الاختبار الشامل', 'success');
      
    } catch (error) {
      addTestResult(`❌ خطأ في الاختبار الشامل: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  // فتح حوار الاختبار
  const openTestDialog = (type, title) => {
    setTestDialog({ open: true, type, title });
  };

  // إغلاق حوار الاختبار
  const closeTestDialog = () => {
    setTestDialog({ open: false, type: '', title: '' });
  };

  // تنفيذ اختبار محدد
  const executeTest = async () => {
    closeTestDialog();
    setLoading(true);
    
    try {
      switch (testDialog.type) {
        case 'student':
          await testAddStudent();
          break;
        case 'course':
          await testAddCourse();
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('خطأ في تنفيذ الاختبار:', error);
    } finally {
      setLoading(false);
    }
  };

  // الحصول على أيقونة النتيجة
  const getResultIcon = (type) => {
    switch (type) {
      case 'success': return <CheckCircle color="success" />;
      case 'error': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      default: return <TestTube color="info" />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <TestTube color="primary" />
        اختبار وظائف الإضافة
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        هذه الصفحة تختبر وظائف إضافة الطلاب والكورسات للتأكد من عملها بدون أخطاء
      </Typography>

      {/* بيانات الاختبار */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            بيانات الاختبار
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم الطالب"
                value={testData.studentName}
                onChange={(e) => setTestData(prev => ({ ...prev, studentName: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="بريد الطالب"
                value={testData.studentEmail}
                onChange={(e) => setTestData(prev => ({ ...prev, studentEmail: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="عنوان الكورس"
                value={testData.courseTitle}
                onChange={(e) => setTestData(prev => ({ ...prev, courseTitle: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="وصف الكورس"
                value={testData.courseDescription}
                onChange={(e) => setTestData(prev => ({ ...prev, courseDescription: e.target.value }))}
                size="small"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* أزرار الاختبار */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item>
          <Button
            variant="contained"
            onClick={runFullTest}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <PlayArrow />}
          >
            {loading ? 'جاري الاختبار...' : 'اختبار شامل'}
          </Button>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            onClick={() => openTestDialog('student', 'اختبار إضافة طالب')}
            disabled={loading}
            startIcon={<People />}
          >
            اختبار إضافة طالب
          </Button>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            onClick={() => openTestDialog('course', 'اختبار إضافة كورس')}
            disabled={loading}
            startIcon={<School />}
          >
            اختبار إضافة كورس
          </Button>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            onClick={() => setTestResults([])}
            disabled={loading}
          >
            مسح النتائج
          </Button>
        </Grid>
      </Grid>

      {/* نتائج الاختبار */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            نتائج الاختبار
          </Typography>
          
          {testResults.length === 0 ? (
            <Alert severity="info">
              لم يتم تشغيل أي اختبار بعد. اضغط على "اختبار شامل" للبدء.
            </Alert>
          ) : (
            <List>
              {testResults.map((result, index) => (
                <React.Fragment key={result.id}>
                  <ListItem>
                    <ListItemIcon>
                      {getResultIcon(result.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={result.message}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {result.timestamp}
                          </Typography>
                          {result.details && (
                            <Box sx={{ mt: 1 }}>
                              {Object.entries(result.details).map(([key, value]) => (
                                <Chip
                                  key={key}
                                  label={`${key}: ${value}`}
                                  size="small"
                                  sx={{ mr: 1, mb: 0.5 }}
                                />
                              ))}
                            </Box>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < testResults.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* حوار تأكيد الاختبار */}
      <Dialog
        open={testDialog.open}
        onClose={closeTestDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {testDialog.title}
        </DialogTitle>
        <DialogContent>
          <Typography>
            هل تريد تشغيل هذا الاختبار؟
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeTestDialog}>
            إلغاء
          </Button>
          <Button
            onClick={executeTest}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <PlayArrow />}
          >
            تشغيل الاختبار
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AddFunctionsTest;
