/**
 * نظام الكورسات الذكي المتطور - Skills World Academy
 * Smart Course Management System with Advanced Features
 */

import { db } from '../firebase/config';
import { supabase } from '../supabase/config';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  serverTimestamp,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  onSnapshot,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';
import toast from 'react-hot-toast';

/**
 * خدمة الكورسات الذكية المتطورة
 */
export class SmartCourseService {
  constructor() {
    this.listeners = new Map();
    this.cache = new Map();
    this.analytics = {
      enrollments: 0,
      completions: 0,
      averageProgress: 0
    };
  }

  /**
   * تسجيل طالب في كورس مع نظام ذكي متطور
   */
  async enrollStudentInCourse(studentId, courseId, options = {}) {
    try {
      console.log('🚀 بدء التسجيل الذكي للطالب في الكورس...');
      console.log('📊 بيانات التسجيل:', { studentId, courseId, options });

      // التحقق من صحة البيانات
      if (!studentId || !courseId) {
        throw new Error('معرف الطالب ومعرف الكورس مطلوبان');
      }

      // التحقق من عدم وجود تسجيل مسبق
      const existingEnrollment = await this.checkExistingEnrollment(studentId, courseId);
      if (existingEnrollment) {
        throw new Error('الطالب مسجل في هذا الكورس مسبقاً');
      }

      // جلب بيانات الطالب والكورس
      const [student, course] = await Promise.all([
        this.getStudentData(studentId),
        this.getCourseData(courseId)
      ]);

      if (!student) throw new Error('الطالب غير موجود');
      if (!course) throw new Error('الكورس غير موجود');

      // إنشاء بيانات التسجيل الذكية
      const enrollmentData = {
        studentId,
        courseId,
        studentName: student.name,
        studentCode: student.studentCode,
        courseTitle: course.title,
        courseInstructor: course.instructor,
        
        // بيانات التقدم
        progress: 0,
        completedLessons: [],
        totalLessons: course.totalVideos || 0,
        currentLesson: 0,
        
        // بيانات الحالة
        status: 'active',
        isCompleted: false,
        isDeleted: false,
        isPaused: false,
        
        // بيانات التوقيت
        enrolledAt: serverTimestamp(),
        lastAccessed: serverTimestamp(),
        estimatedCompletionDate: this.calculateEstimatedCompletion(course),
        
        // بيانات التفاعل
        watchTime: 0,
        interactionScore: 0,
        engagementLevel: 'beginner',
        
        // بيانات إضافية
        enrollmentSource: options.source || 'admin',
        priority: options.priority || 'normal',
        notes: options.notes || '',
        
        // بيانات التحليلات
        analytics: {
          sessionsCount: 0,
          averageSessionTime: 0,
          lastSessionDate: null,
          deviceType: 'unknown',
          preferredTime: 'unknown'
        }
      };

      // استخدام batch للعمليات المتعددة
      const batch = writeBatch(db);

      // إضافة التسجيل
      const enrollmentRef = doc(collection(db, 'enrollments'));
      batch.set(enrollmentRef, enrollmentData);

      // تحديث إحصائيات الكورس
      const courseRef = doc(db, 'courses', courseId);
      batch.update(courseRef, {
        enrolledStudents: increment(1),
        lastEnrollmentDate: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // تحديث إحصائيات الطالب
      const studentRef = doc(db, 'users', studentId);
      batch.update(studentRef, {
        enrolledCourses: increment(1),
        enrolledCoursesList: arrayUnion(courseId),
        lastEnrollmentDate: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // إضافة إشعار ذكي للطالب
      const notificationRef = doc(collection(db, 'notifications'));
      batch.set(notificationRef, {
        userId: studentId,
        type: 'course_enrollment',
        title: '🎉 تم تسجيلك في كورس جديد!',
        message: `مرحباً ${student.name}! تم تسجيلك بنجاح في كورس "${course.title}". ابدأ رحلة التعلم الآن!`,
        data: {
          courseId,
          enrollmentId: enrollmentRef.id,
          courseTitle: course.title,
          instructor: course.instructor,
          estimatedDuration: course.duration
        },
        isRead: false,
        priority: 'high',
        actionRequired: true,
        actionText: 'ابدأ التعلم',
        actionUrl: `/course/${courseId}`,
        createdAt: serverTimestamp(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // ينتهي بعد أسبوع
      });

      // إضافة سجل نشاط
      const activityRef = doc(collection(db, 'activities'));
      batch.set(activityRef, {
        type: 'enrollment',
        studentId,
        courseId,
        studentName: student.name,
        courseTitle: course.title,
        description: `تم تسجيل الطالب ${student.name} في كورس ${course.title}`,
        timestamp: serverTimestamp(),
        metadata: {
          enrollmentSource: options.source || 'admin',
          adminId: options.adminId || 'system'
        }
      });

      // تنفيذ جميع العمليات
      await batch.commit();

      console.log('✅ تم تسجيل الطالب بنجاح:', enrollmentRef.id);

      // إرسال إشعار فوري للطالب (إذا كان متصلاً)
      await this.sendRealtimeNotification(studentId, {
        type: 'enrollment_success',
        title: 'تم التسجيل بنجاح! 🎉',
        message: `تم تسجيلك في كورس "${course.title}"`,
        courseId,
        enrollmentId: enrollmentRef.id
      });

      // تحديث التحليلات
      await this.updateAnalytics('enrollment', { studentId, courseId });

      // محاولة المزامنة مع Supabase (اختياري)
      try {
        await this.syncEnrollmentWithSupabase(enrollmentRef.id, enrollmentData);
      } catch (supabaseError) {
        console.warn('⚠️ تحذير: فشل في مزامنة Supabase:', supabaseError.message);
      }

      return {
        success: true,
        enrollmentId: enrollmentRef.id,
        message: `تم تسجيل ${student.name} في كورس "${course.title}" بنجاح`,
        data: {
          ...enrollmentData,
          id: enrollmentRef.id
        }
      };

    } catch (error) {
      console.error('❌ خطأ في تسجيل الطالب:', error);
      
      // تسجيل الخطأ للتحليل
      await this.logError('enrollment_failed', error, { studentId, courseId });
      
      throw error;
    }
  }

  /**
   * التحقق من وجود تسجيل مسبق
   */
  async checkExistingEnrollment(studentId, courseId) {
    try {
      const q = query(
        collection(db, 'enrollments'),
        where('studentId', '==', studentId),
        where('courseId', '==', courseId),
        where('isDeleted', '==', false)
      );
      
      const snapshot = await getDocs(q);
      return !snapshot.empty ? snapshot.docs[0].data() : null;
    } catch (error) {
      console.error('خطأ في التحقق من التسجيل المسبق:', error);
      return null;
    }
  }

  /**
   * جلب بيانات الطالب
   */
  async getStudentData(studentId) {
    try {
      const q = query(
        collection(db, 'users'),
        where('__name__', '==', studentId),
        limit(1)
      );
      
      const snapshot = await getDocs(q);
      if (snapshot.empty) return null;
      
      return { id: snapshot.docs[0].id, ...snapshot.docs[0].data() };
    } catch (error) {
      console.error('خطأ في جلب بيانات الطالب:', error);
      return null;
    }
  }

  /**
   * جلب بيانات الكورس
   */
  async getCourseData(courseId) {
    try {
      const q = query(
        collection(db, 'courses'),
        where('__name__', '==', courseId),
        limit(1)
      );
      
      const snapshot = await getDocs(q);
      if (snapshot.empty) return null;
      
      return { id: snapshot.docs[0].id, ...snapshot.docs[0].data() };
    } catch (error) {
      console.error('خطأ في جلب بيانات الكورس:', error);
      return null;
    }
  }

  /**
   * حساب تاريخ الإنجاز المتوقع
   */
  calculateEstimatedCompletion(course) {
    const durationInDays = this.parseDuration(course.duration || '4 أسابيع');
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + durationInDays);
    return estimatedDate;
  }

  /**
   * تحليل مدة الكورس
   */
  parseDuration(duration) {
    const weeks = duration.match(/(\d+)\s*أسبوع/);
    const days = duration.match(/(\d+)\s*يوم/);
    
    if (weeks) return parseInt(weeks[1]) * 7;
    if (days) return parseInt(days[1]);
    return 28; // افتراضي 4 أسابيع
  }

  /**
   * إرسال إشعار فوري
   */
  async sendRealtimeNotification(userId, notification) {
    try {
      // يمكن إضافة WebSocket أو Server-Sent Events هنا
      console.log('📱 إرسال إشعار فوري للطالب:', userId, notification);
    } catch (error) {
      console.warn('تحذير في إرسال الإشعار الفوري:', error);
    }
  }

  /**
   * تحديث التحليلات
   */
  async updateAnalytics(action, data) {
    try {
      const analyticsRef = doc(collection(db, 'analytics'));
      await addDoc(collection(db, 'analytics'), {
        action,
        data,
        timestamp: serverTimestamp(),
        date: new Date().toISOString().split('T')[0]
      });
    } catch (error) {
      console.warn('تحذير في تحديث التحليلات:', error);
    }
  }

  /**
   * مزامنة مع Supabase
   */
  async syncEnrollmentWithSupabase(enrollmentId, enrollmentData) {
    try {
      const { error } = await supabase
        .from('enrollments')
        .insert([{
          firebase_id: enrollmentId,
          ...enrollmentData,
          created_at: new Date().toISOString()
        }]);

      if (error) throw error;
      console.log('✅ تم مزامنة التسجيل مع Supabase');
    } catch (error) {
      console.warn('⚠️ فشل في مزامنة Supabase:', error);
      throw error;
    }
  }

  /**
   * تسجيل الأخطاء
   */
  async logError(type, error, context) {
    try {
      await addDoc(collection(db, 'error_logs'), {
        type,
        message: error.message,
        stack: error.stack,
        context,
        timestamp: serverTimestamp(),
        resolved: false
      });
    } catch (logError) {
      console.error('فشل في تسجيل الخطأ:', logError);
    }
  }
}

// إنشاء instance واحد للاستخدام
export const smartCourseService = new SmartCourseService();

// تصدير افتراضي
export default smartCourseService;
